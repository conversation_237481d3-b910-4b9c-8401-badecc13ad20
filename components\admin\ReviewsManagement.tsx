"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { RefreshCw, Star, Trash2, Eye, EyeOff, Search, Filter } from "lucide-react";
import { useEffect, useState } from "react";

interface CachedReview {
  id: string;
  external_id: string;
  author_name: string;
  author_initials: string;
  rating: number;
  text: string;
  date: string;
  relative_time: string;
  profile_photo_url?: string;
  source: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface ReviewsResponse {
  success: boolean;
  reviews: CachedReview[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export function ReviewsManagement() {
  const [reviews, setReviews] = useState<CachedReview[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedReviews, setSelectedReviews] = useState<string[]>([]);
  const [filters, setFilters] = useState({
    source: "",
    isActive: "",
    minRating: "",
    search: "",
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  });

  const { toast } = useToast();

  const fetchReviews = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v !== "")),
      });

      const response = await fetch(`/api/admin/reviews?${params}`);
      const data: ReviewsResponse = await response.json();

      if (data.success) {
        setReviews(data.reviews);
        setPagination(data.pagination);
      } else {
        toast({
          title: "Erreur",
          description: "Impossible de charger les avis",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error fetching reviews:", error);
      toast({
        title: "Erreur",
        description: "Erreur réseau lors du chargement des avis",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const refreshReviews = async () => {
    try {
      setRefreshing(true);
      const response = await fetch("/api/admin/reviews", { method: "POST" });
      const data = await response.json();

      if (data.success) {
        toast({
          title: "Succès",
          description: `${data.count} avis mis à jour depuis Google`,
        });
        await fetchReviews(); // Reload the list
      } else {
        toast({
          title: "Erreur",
          description: data.details || "Impossible de mettre à jour les avis",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error refreshing reviews:", error);
      toast({
        title: "Erreur",
        description: "Erreur lors de la mise à jour des avis",
        variant: "destructive",
      });
    } finally {
      setRefreshing(false);
    }
  };

  const toggleReviewsVisibility = async (isActive: boolean) => {
    if (selectedReviews.length === 0) {
      toast({
        title: "Attention",
        description: "Veuillez sélectionner au moins un avis",
        variant: "destructive",
      });
      return;
    }

    try {
      const response = await fetch("/api/admin/reviews", {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ reviewIds: selectedReviews, isActive }),
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: "Succès",
          description: data.message,
        });
        setSelectedReviews([]);
        await fetchReviews();
      } else {
        toast({
          title: "Erreur",
          description: "Impossible de modifier les avis",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error updating reviews:", error);
      toast({
        title: "Erreur",
        description: "Erreur lors de la modification des avis",
        variant: "destructive",
      });
    }
  };

  const deleteReviews = async () => {
    if (selectedReviews.length === 0) {
      toast({
        title: "Attention",
        description: "Veuillez sélectionner au moins un avis",
        variant: "destructive",
      });
      return;
    }

    if (!confirm(`Êtes-vous sûr de vouloir supprimer ${selectedReviews.length} avis ?`)) {
      return;
    }

    try {
      const response = await fetch("/api/admin/reviews", {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ reviewIds: selectedReviews }),
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: "Succès",
          description: data.message,
        });
        setSelectedReviews([]);
        await fetchReviews();
      } else {
        toast({
          title: "Erreur",
          description: "Impossible de supprimer les avis",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error deleting reviews:", error);
      toast({
        title: "Erreur",
        description: "Erreur lors de la suppression des avis",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    fetchReviews();
  }, [pagination.page, filters]);

  const toggleSelectAll = () => {
    if (selectedReviews.length === reviews.length) {
      setSelectedReviews([]);
    } else {
      setSelectedReviews(reviews.map(r => r.id));
    }
  };

  const toggleSelectReview = (reviewId: string) => {
    setSelectedReviews(prev =>
      prev.includes(reviewId)
        ? prev.filter(id => id !== reviewId)
        : [...prev, reviewId]
    );
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Gestion des Avis Google</span>
            <Button
              onClick={refreshReviews}
              disabled={refreshing}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
              Actualiser depuis Google
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label htmlFor="search">Recherche</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 w-4 h-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Nom ou texte..."
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="source">Source</Label>
              <Select value={filters.source} onValueChange={(value) => setFilters(prev => ({ ...prev, source: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Toutes les sources" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Toutes les sources</SelectItem>
                  <SelectItem value="google_places">Google Places</SelectItem>
                  <SelectItem value="google_business">Google Business</SelectItem>
                  <SelectItem value="manual">Manuel</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="isActive">Statut</Label>
              <Select value={filters.isActive} onValueChange={(value) => setFilters(prev => ({ ...prev, isActive: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Tous les statuts" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Tous les statuts</SelectItem>
                  <SelectItem value="true">Actif</SelectItem>
                  <SelectItem value="false">Inactif</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="minRating">Note minimum</Label>
              <Select value={filters.minRating} onValueChange={(value) => setFilters(prev => ({ ...prev, minRating: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Toutes les notes" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Toutes les notes</SelectItem>
                  <SelectItem value="5">5 étoiles</SelectItem>
                  <SelectItem value="4">4+ étoiles</SelectItem>
                  <SelectItem value="3">3+ étoiles</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Separator />

          {/* Bulk Actions */}
          {selectedReviews.length > 0 && (
            <div className="flex items-center gap-2 p-4 bg-blue-50 rounded-lg">
              <span className="text-sm text-blue-700">
                {selectedReviews.length} avis sélectionné(s)
              </span>
              <Button
                size="sm"
                variant="outline"
                onClick={() => toggleReviewsVisibility(true)}
                className="flex items-center gap-1"
              >
                <Eye className="w-4 h-4" />
                Activer
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => toggleReviewsVisibility(false)}
                className="flex items-center gap-1"
              >
                <EyeOff className="w-4 h-4" />
                Désactiver
              </Button>
              <Button
                size="sm"
                variant="destructive"
                onClick={deleteReviews}
                className="flex items-center gap-1"
              >
                <Trash2 className="w-4 h-4" />
                Supprimer
              </Button>
            </div>
          )}

          {/* Reviews List */}
          <div className="space-y-4">
            {/* Header */}
            <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
              <Checkbox
                checked={selectedReviews.length === reviews.length && reviews.length > 0}
                onCheckedChange={toggleSelectAll}
              />
              <span className="font-medium">Tout sélectionner</span>
            </div>

            {loading ? (
              <div className="text-center py-8">
                <RefreshCw className="w-6 h-6 animate-spin mx-auto mb-2" />
                <p>Chargement des avis...</p>
              </div>
            ) : reviews.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                Aucun avis trouvé
              </div>
            ) : (
              reviews.map((review) => (
                <Card key={review.id} className={`${!review.is_active ? 'opacity-60' : ''}`}>
                  <CardContent className="p-4">
                    <div className="flex items-start gap-4">
                      <Checkbox
                        checked={selectedReviews.includes(review.id)}
                        onCheckedChange={() => toggleSelectReview(review.id)}
                      />
                      
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{review.author_name}</span>
                            <div className="flex">
                              {[...Array(review.rating)].map((_, i) => (
                                <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                              ))}
                            </div>
                            <Badge variant={review.is_active ? "default" : "secondary"}>
                              {review.is_active ? "Actif" : "Inactif"}
                            </Badge>
                            <Badge variant="outline">{review.source}</Badge>
                          </div>
                          <span className="text-sm text-gray-500">{review.relative_time}</span>
                        </div>
                        
                        <p className="text-gray-700">{review.text}</p>
                        
                        <div className="text-xs text-gray-500">
                          Ajouté le {new Date(review.created_at).toLocaleDateString('fr-FR')}
                          {review.updated_at !== review.created_at && (
                            <span> • Modifié le {new Date(review.updated_at).toLocaleDateString('fr-FR')}</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-500">
                {pagination.total} avis au total
              </span>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={pagination.page === 1}
                  onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                >
                  Précédent
                </Button>
                <span className="text-sm">
                  Page {pagination.page} sur {pagination.totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={pagination.page === pagination.totalPages}
                  onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                >
                  Suivant
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
