import { getServerSideSitemap } from 'next-sitemap';
import { supabase } from '@/lib/supabase';

export async function GET() {
  // Fetch all active services from the database
  const { data: services } = await supabase
    .from('services')
    .select('id, slug, name, updated_at')
    .eq('is_active', true);

  // Generate sitemap fields for services
  const serviceFields = (services || []).map((service) => ({
    loc: `https://www.soleiletdecouverte.com/services/${service.slug || service.id}`,
    lastmod: service.updated_at || new Date().toISOString(),
    changefreq: 'monthly' as const,
    priority: 0.8,
  }));

  // You can add other dynamic content here
  // For example, blog posts, events, etc.

  return getServerSideSitemap(serviceFields);
}
