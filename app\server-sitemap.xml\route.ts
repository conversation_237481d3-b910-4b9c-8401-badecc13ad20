import { getServerSideSitemap } from "next-sitemap";
import { supabase } from "@/lib/supabase";

export async function GET() {
	try {
		// Fetch all active services from the database
		const { data: services, error } = await supabase
			.from("services")
			.select("id, name, updated_at")
			.eq("is_active", true)
			.order("name");

		if (error) {
			console.error("Error fetching services for sitemap:", error);
			return getServerSideSitemap([]);
		}

		// Generate sitemap fields for services
		const serviceFields = (services || []).map((service) => {
			// Use slug if available, otherwise fall back to ID
			const serviceSlug = service.slug || service.id;

			return {
				loc: `https://www.soleiletdecouverte.com/services/${serviceSlug}`,
				lastmod: service.updated_at || new Date().toISOString(),
				changefreq: "monthly" as const,
				priority: 0.8,
			};
		});

		// You can add other dynamic content here
		// For example, blog posts, events, etc.
		const additionalFields = [
			// Example: Add blog posts if you have them
			// {
			//   loc: 'https://www.soleiletdecouverte.com/blog',
			//   lastmod: new Date().toISOString(),
			//   changefreq: 'weekly',
			//   priority: 0.7,
			// },
			// Example: Add special pages
			// {
			//   loc: 'https://www.soleiletdecouverte.com/special-offers',
			//   lastmod: new Date().toISOString(),
			//   changefreq: 'monthly',
			//   priority: 0.6,
			// }
		];

		const allFields = [...serviceFields, ...additionalFields];

		return getServerSideSitemap(allFields);
	} catch (error) {
		console.error("Error generating server sitemap:", error);
		return getServerSideSitemap([]);
	}
}
