<svg width="1200" height="630" viewBox="0 0 1200 630" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0ea5e9;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
    
    <!-- Wave Pattern -->
    <pattern id="waves" x="0" y="0" width="100" height="20" patternUnits="userSpaceOnUse">
      <path d="M0 10 Q25 0 50 10 T100 10 V20 H0 Z" fill="rgba(255,255,255,0.1)"/>
    </pattern>
    
    <!-- Tropical Elements -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#bgGradient)"/>
  
  <!-- Wave Pattern Overlay -->
  <rect width="1200" height="630" fill="url(#waves)" opacity="0.3"/>
  
  <!-- Decorative Elements -->
  <!-- Palm Leaves -->
  <g opacity="0.2">
    <path d="M50 100 Q80 80 100 120 Q120 80 150 100" stroke="#ffffff" stroke-width="3" fill="none"/>
    <path d="M1050 500 Q1080 480 1100 520 Q1120 480 1150 500" stroke="#ffffff" stroke-width="3" fill="none"/>
  </g>
  
  <!-- Sun/Circle Element -->
  <circle cx="1000" cy="150" r="80" fill="rgba(255,255,255,0.15)" filter="url(#glow)"/>
  <circle cx="1000" cy="150" r="60" fill="rgba(255,255,255,0.1)"/>
  
  <!-- Main Content Container -->
  <rect x="80" y="120" width="1040" height="390" rx="20" fill="rgba(255,255,255,0.95)" opacity="0.95"/>
  
  <!-- Logo Area (Left Side) -->
  <g transform="translate(120, 180)">
    <!-- Logo Circle Background -->
    <circle cx="80" cy="80" r="70" fill="#10b981"/>
    <circle cx="80" cy="80" r="60" fill="#ffffff"/>
    
    <!-- Simplified Logo Icon -->
    <g transform="translate(80, 80)">
      <!-- Water/Wave Icon -->
      <path d="M-30 -10 Q-15 -20 0 -10 Q15 0 30 -10" stroke="#10b981" stroke-width="4" fill="none"/>
      <path d="M-30 0 Q-15 -10 0 0 Q15 10 30 0" stroke="#10b981" stroke-width="4" fill="none"/>
      <path d="M-30 10 Q-15 0 0 10 Q15 20 30 10" stroke="#10b981" stroke-width="4" fill="none"/>
      
      <!-- Sun rays -->
      <g stroke="#f59e0b" stroke-width="3">
        <line x1="0" y1="-35" x2="0" y2="-45"/>
        <line x1="25" y1="-25" x2="32" y2="-32"/>
        <line x1="35" y1="0" x2="45" y2="0"/>
        <line x1="25" y1="25" x2="32" y2="32"/>
        <line x1="0" y1="35" x2="0" y2="45"/>
        <line x1="-25" y1="25" x2="-32" y2="32"/>
        <line x1="-35" y1="0" x2="-45" y2="0"/>
        <line x1="-25" y1="-25" x2="-32" y2="-32"/>
      </g>
    </g>
  </g>
  
  <!-- Text Content (Right Side) -->
  <g transform="translate(300, 200)">
    <!-- Main Title -->
    <text x="0" y="0" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="#1f2937">
      <tspan x="0" dy="0">Soleil et Découverte</tspan>
    </text>
    
    <!-- Subtitle -->
    <text x="0" y="60" font-family="Arial, sans-serif" font-size="28" fill="#10b981" font-weight="600">
      <tspan x="0" dy="0">Excursions éco-responsables</tspan>
    </text>
    
    <!-- Location -->
    <text x="0" y="100" font-family="Arial, sans-serif" font-size="24" fill="#6b7280">
      <tspan x="0" dy="0">🌴 Guadeloupe • Petit-Canal</tspan>
    </text>
    
    <!-- Features -->
    <g transform="translate(0, 140)">
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="20" fill="#374151">
        <tspan x="0" dy="0">🚤 WaterBikes • 🏛️ Visites culturelles • 🦆 Rencontres pélicans</tspan>
      </text>
    </g>
  </g>
  
  <!-- Bottom Accent -->
  <rect x="0" y="580" width="1200" height="50" fill="rgba(16, 185, 129, 0.1)"/>
  
  <!-- Website URL -->
  <text x="600" y="610" font-family="Arial, sans-serif" font-size="18" fill="#6b7280" text-anchor="middle">
    www.soleiletdecouverte.com
  </text>
</svg>
