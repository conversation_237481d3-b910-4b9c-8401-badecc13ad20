"use client";

import { useEffect, useRef } from "react";

interface SocialCardProps {
  title?: string;
  subtitle?: string;
  description?: string;
  type?: "homepage" | "service" | "contact";
  serviceImage?: string;
}

export default function SocialCardGenerator({
  title = "Soleil et Découverte",
  subtitle = "Excursions éco-responsables",
  description = "🌴 Guadeloupe • Petit-Canal",
  type = "homepage",
  serviceImage
}: SocialCardProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Set canvas size for Open Graph (1200x630)
    canvas.width = 1200;
    canvas.height = 630;

    // Create gradient background
    const gradient = ctx.createLinearGradient(0, 0, 1200, 630);
    gradient.addColorStop(0, "#0ea5e9");
    gradient.addColorStop(0.5, "#10b981");
    gradient.addColorStop(1, "#059669");

    // Fill background
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 1200, 630);

    // Add wave pattern overlay
    ctx.globalAlpha = 0.1;
    ctx.fillStyle = "#ffffff";
    for (let i = 0; i < 12; i++) {
      const y = i * 60;
      ctx.beginPath();
      ctx.moveTo(0, y);
      for (let x = 0; x <= 1200; x += 50) {
        const waveY = y + Math.sin((x / 50) * Math.PI) * 10;
        ctx.lineTo(x, waveY);
      }
      ctx.lineTo(1200, y + 20);
      ctx.lineTo(0, y + 20);
      ctx.closePath();
      ctx.fill();
    }

    // Reset alpha
    ctx.globalAlpha = 1;

    // Main content background
    ctx.fillStyle = "rgba(255, 255, 255, 0.95)";
    ctx.roundRect(80, 120, 1040, 390, 20);
    ctx.fill();

    // Logo area
    ctx.fillStyle = "#10b981";
    ctx.beginPath();
    ctx.arc(200, 260, 70, 0, 2 * Math.PI);
    ctx.fill();

    ctx.fillStyle = "#ffffff";
    ctx.beginPath();
    ctx.arc(200, 260, 60, 0, 2 * Math.PI);
    ctx.fill();

    // Logo icon (simplified water waves)
    ctx.strokeStyle = "#10b981";
    ctx.lineWidth = 4;
    ctx.lineCap = "round";

    // Water waves
    for (let i = 0; i < 3; i++) {
      ctx.beginPath();
      const y = 250 + (i * 10);
      ctx.moveTo(170, y);
      ctx.quadraticCurveTo(185, y - 10, 200, y);
      ctx.quadraticCurveTo(215, y + 10, 230, y);
      ctx.stroke();
    }

    // Sun rays
    ctx.strokeStyle = "#f59e0b";
    ctx.lineWidth = 3;
    const rayLength = 15;
    for (let i = 0; i < 8; i++) {
      const angle = (i * Math.PI) / 4;
      const startX = 200 + Math.cos(angle) * 35;
      const startY = 260 + Math.sin(angle) * 35;
      const endX = 200 + Math.cos(angle) * (35 + rayLength);
      const endY = 260 + Math.sin(angle) * (35 + rayLength);
      
      ctx.beginPath();
      ctx.moveTo(startX, startY);
      ctx.lineTo(endX, endY);
      ctx.stroke();
    }

    // Text content
    ctx.fillStyle = "#1f2937";
    ctx.font = "bold 48px Arial, sans-serif";
    ctx.fillText(title, 320, 220);

    ctx.fillStyle = "#10b981";
    ctx.font = "600 28px Arial, sans-serif";
    ctx.fillText(subtitle, 320, 260);

    ctx.fillStyle = "#6b7280";
    ctx.font = "24px Arial, sans-serif";
    ctx.fillText(description, 320, 300);

    // Features
    ctx.fillStyle = "#374151";
    ctx.font = "20px Arial, sans-serif";
    const features = "🚤 WaterBikes • 🏛️ Visites culturelles • 🦆 Rencontres pélicans";
    ctx.fillText(features, 320, 340);

    // Bottom accent
    ctx.fillStyle = "rgba(16, 185, 129, 0.1)";
    ctx.fillRect(0, 580, 1200, 50);

    // Website URL
    ctx.fillStyle = "#6b7280";
    ctx.font = "18px Arial, sans-serif";
    ctx.textAlign = "center";
    ctx.fillText("www.soleiletdecouverte.com", 600, 610);

  }, [title, subtitle, description, type, serviceImage]);

  return (
    <canvas
      ref={canvasRef}
      style={{ display: "none" }}
      width={1200}
      height={630}
    />
  );
}

// Helper function to generate social card URL
export function generateSocialCardUrl(params: {
  title?: string;
  subtitle?: string;
  type?: string;
}) {
  const searchParams = new URLSearchParams();
  
  if (params.title) searchParams.set("title", params.title);
  if (params.subtitle) searchParams.set("subtitle", params.subtitle);
  if (params.type) searchParams.set("type", params.type);

  return `/api/social-card?${searchParams.toString()}`;
}
