/** @type {import('next-sitemap').IConfig} */
module.exports = {
	siteUrl: process.env.SITE_URL || "https://www.soleiletdecouverte.com",
	generateRobotsTxt: true,
	generateIndexSitemap: false,

	// Exclude admin routes and API routes from sitemap
	exclude: ["/admin/*", "/api/*", "/debug/*", "/verify-booking/*", "/reservation/confirmation"],

	// Additional paths to include (for dynamic routes and static pages)
	additionalPaths: async (config) => {
		const result = [];

		// Add main static pages explicitly
		const mainPages = [
			{ loc: "/", priority: 1.0, changefreq: "weekly" },
			{ loc: "/services", priority: 0.9, changefreq: "weekly" },
			{ loc: "/contact", priority: 0.8, changefreq: "monthly" },
			{ loc: "/reservation", priority: 0.9, changefreq: "weekly" },
			{ loc: "/_about", priority: 0.7, changefreq: "monthly" },
			{ loc: "/_gallery", priority: 0.7, changefreq: "monthly" },
		];

		mainPages.forEach((page) => {
			result.push({
				loc: page.loc,
				changefreq: page.changefreq,
				priority: page.priority,
				lastmod: new Date().toISOString(),
			});
		});

		// Add service detail pages
		const commonServices = [
			"waterbikes-excursion",
			"visite-culturelle-guidee",
			"rencontre-pelicans",
			"exploration-mangrove",
			"degustation-produits-locaux",
			"sortie-peche",
			"tour-ilets",
			"balade-nature",
		];

		commonServices.forEach((serviceSlug) => {
			result.push({
				loc: `/services/${serviceSlug}`,
				changefreq: "monthly",
				priority: 0.8,
				lastmod: new Date().toISOString(),
			});
		});

		return result;
	},

	// Custom transformation for specific pages
	transform: async (config, path) => {
		// Custom priority and changefreq for different page types
		const customConfig = {
			loc: path,
			lastmod: new Date().toISOString(),
		};

		// Homepage
		if (path === "/") {
			return {
				...customConfig,
				priority: 1.0,
				changefreq: "weekly",
			};
		}

		// Main service pages
		if (path === "/services" || path === "/reservation") {
			return {
				...customConfig,
				priority: 0.9,
				changefreq: "weekly",
			};
		}

		// Contact page
		if (path === "/contact") {
			return {
				...customConfig,
				priority: 0.8,
				changefreq: "monthly",
			};
		}

		// About and gallery pages
		if (path === "/_about" || path === "/_gallery") {
			return {
				...customConfig,
				priority: 0.7,
				changefreq: "monthly",
			};
		}

		// Service detail pages
		if (path.startsWith("/services/")) {
			return {
				...customConfig,
				priority: 0.8,
				changefreq: "monthly",
			};
		}

		// Default configuration
		return {
			...customConfig,
			priority: 0.5,
			changefreq: "monthly",
		};
	},

	// Robots.txt configuration
	robotsTxtOptions: {
		policies: [
			{
				userAgent: "*",
				allow: "/",
				disallow: ["/admin/", "/api/", "/debug/", "/verify-booking/", "/reservation/confirmation"],
			},
		],
		additionalSitemaps: [
			// Dynamic sitemap for services and other dynamic content
			"https://www.soleiletdecouverte.com/server-sitemap.xml",
		],
	},
};
