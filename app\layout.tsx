import { AuthProvider } from "@/lib/auth-context";
import { Analytics } from "@vercel/analytics/next";
import type { Metadata } from "next";
import StructuredData from "@/components/StructuredData";
import "./globals.css";

export const metadata: Metadata = {
	title: {
		default: "Soleil et Découverte - Excursions éco-responsables en Guadeloupe",
		template: "%s | Soleil et Découverte",
	},
	description:
		"Découvrez la Guadeloupe authentique avec nos excursions éco-responsables. WaterBikes, visites culturelles et rencontres avec les pélicans.",
	keywords: "Guadeloupe, excursions, WaterBikes, écotourisme, pélicans, visites culturelles, Petit-Canal",
	authors: [{ name: "<PERSON>eil et Découverte" }],
	creator: "Soleil et Découverte",
	publisher: "Soleil et Découverte",
	formatDetection: {
		email: false,
		address: false,
		telephone: false,
	},
	metadataBase: new URL("https://www.soleiletdecouverte.com"),
	alternates: {
		canonical: "/",
	},
	openGraph: {
		title: "Soleil et Découverte - Excursions éco-responsables en Guadeloupe",
		description:
			"Découvrez la Guadeloupe authentique avec nos excursions éco-responsables. WaterBikes, visites culturelles et rencontres avec les pélicans.",
		url: "https://www.soleiletdecouverte.com",
		siteName: "Soleil et Découverte",
		images: [
			{
				url: "/images/og-social-card.svg",
				width: 1200,
				height: 630,
				alt: "Soleil et Découverte - Excursions éco-responsables en Guadeloupe",
				type: "image/svg+xml",
			},
			{
				url: "/images/home_hero.png",
				width: 1200,
				height: 630,
				alt: "Soleil et Découverte - Excursions en Guadeloupe",
				type: "image/png",
			},
		],
		locale: "fr_FR",
		type: "website",
	},
	twitter: {
		card: "summary_large_image",
		title: "Soleil et Découverte - Excursions éco-responsables en Guadeloupe",
		description:
			"Découvrez la Guadeloupe authentique avec nos excursions éco-responsables. WaterBikes, visites culturelles et rencontres avec les pélicans.",
		images: ["/images/og-social-card.svg"],
	},
	robots: {
		index: true,
		follow: true,
		googleBot: {
			index: true,
			follow: true,
			"max-video-preview": -1,
			"max-image-preview": "large",
			"max-snippet": -1,
		},
	},
	icons: {
		icon: [
			{ url: "/favicon.ico", sizes: "48x48" },
			{ url: "/images/logo-hd.png", sizes: "32x32", type: "image/png" },
			{ url: "/images/logo-hd.png", sizes: "16x16", type: "image/png" },
		],
		shortcut: "/favicon.ico",
		apple: [
			{ url: "/images/logo-hd.png", sizes: "180x180", type: "image/png" },
			{ url: "/images/logo-hd.png", sizes: "152x152", type: "image/png" },
			{ url: "/images/logo-hd.png", sizes: "120x120", type: "image/png" },
		],
		other: [
			{
				rel: "icon",
				url: "/images/logo-hd.png",
				sizes: "192x192",
				type: "image/png",
			},
			{
				rel: "icon",
				url: "/images/logo-hd.png",
				sizes: "512x512",
				type: "image/png",
			},
		],
	},
	manifest: "/site.webmanifest",
};

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang="fr">
			<head>
				{/* Favicon links for maximum compatibility */}
				<link rel="icon" href="/favicon.ico" sizes="any" />
				<link rel="icon" href="/images/logo-hd.png" type="image/png" sizes="32x32" />
				<link rel="icon" href="/images/logo-hd.png" type="image/png" sizes="16x16" />
				<link rel="apple-touch-icon" href="/images/logo-hd.png" sizes="180x180" />
				<link rel="apple-touch-icon" href="/images/logo-hd.png" sizes="152x152" />
				<link rel="apple-touch-icon" href="/images/logo-hd.png" sizes="144x144" />
				<link rel="apple-touch-icon" href="/images/logo-hd.png" sizes="120x120" />
				<link rel="apple-touch-icon" href="/images/logo-hd.png" sizes="114x114" />
				<link rel="apple-touch-icon" href="/images/logo-hd.png" sizes="76x76" />
				<link rel="apple-touch-icon" href="/images/logo-hd.png" sizes="72x72" />
				<link rel="apple-touch-icon" href="/images/logo-hd.png" sizes="60x60" />
				<link rel="apple-touch-icon" href="/images/logo-hd.png" sizes="57x57" />

				{/* Web App Manifest */}
				<link rel="manifest" href="/site.webmanifest" />

				{/* Microsoft Tiles */}
				<meta name="msapplication-TileColor" content="#10b981" />
				<meta name="msapplication-TileImage" content="/images/logo-hd.png" />
				<meta name="msapplication-config" content="/browserconfig.xml" />

				{/* Theme colors */}
				<meta name="theme-color" content="#10b981" />
				<meta name="apple-mobile-web-app-status-bar-style" content="default" />

				{/* Preload critical resources */}
				<link rel="preload" href="/images/home_hero.png" as="image" />
				<link rel="preload" href="/images/logo-hd.png" as="image" />
				<link rel="dns-prefetch" href="https://zalzjvuxoffmhaokvzda.supabase.co" />

				{/* Performance optimizations */}
				<meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
				<meta httpEquiv="X-UA-Compatible" content="IE=edge" />
			</head>
			<body>
				<StructuredData type="homepage" />
				<AuthProvider>{children}</AuthProvider>
				<Analytics />
			</body>
		</html>
	);
}
