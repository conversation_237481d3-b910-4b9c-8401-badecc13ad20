import type { Database } from "./types/index";
import type { ServiceOption } from "./types/services";
export type { Database };

// Database table types
export type Service = Database["public"]["Tables"]["services"]["Row"];
export type ServiceInsert = Database["public"]["Tables"]["services"]["Insert"];
export type ServiceUpdate = Database["public"]["Tables"]["services"]["Update"];

export type Equipment = Database["public"]["Tables"]["equipment"]["Row"];
export type EquipmentInsert = Database["public"]["Tables"]["equipment"]["Insert"];

export type PricingTier = Database["public"]["Tables"]["pricing_tiers"]["Row"];
export type PricingTierInsert = Database["public"]["Tables"]["pricing_tiers"]["Insert"];

export type DiscountCoupon = Database["public"]["Tables"]["discount_coupons"]["Row"];
export type DiscountCouponInsert = Database["public"]["Tables"]["discount_coupons"]["Insert"];
export type DiscountCouponUpdate = Database["public"]["Tables"]["discount_coupons"]["Update"];

export type CustomerFeedback = Database["public"]["Tables"]["customer_feedback"]["Row"];
export type CustomerFeedbackInsert = Database["public"]["Tables"]["customer_feedback"]["Insert"];
export type CustomerFeedbackUpdate = Database["public"]["Tables"]["customer_feedback"]["Update"];

export type Reservation = Database["public"]["Tables"]["reservations"]["Row"];
export type ReservationInsert = Database["public"]["Tables"]["reservations"]["Insert"];
export type ReservationUpdate = Database["public"]["Tables"]["reservations"]["Update"];

export type Profile = Database["public"]["Tables"]["profiles"]["Row"];
export type ProfileInsert = Database["public"]["Tables"]["profiles"]["Insert"];

export type Notification = Database["public"]["Tables"]["notifications"]["Row"];
export type NotificationInsert = Database["public"]["Tables"]["notifications"]["Insert"];
export type NotificationUpdate = Database["public"]["Tables"]["notifications"]["Update"];

// UI-specific notification types
export interface UINotification {
	id: string;
	type: "booking" | "payment" | "system" | "message" | "weather";
	title: string;
	message: string;
	timestamp: string;
	read: boolean;
	priority: "low" | "medium" | "high";
	reservation_id?: string;
}

// Extended types for frontend use
export interface ServiceWithPricing extends Omit<Service, "options"> {
	pricing_tiers: PricingTier[];
	equipment_requirements?: {
		equipment: Equipment;
	}[];
	options?: ServiceOption[];
}

// Time slot information generated dynamically from scheduling rules
export interface TimeSlotInfo {
	id?: string; // Synthetic ID for booking API compatibility
	start_time: string;
	end_time: string;
	available_capacity: number;
	is_available: boolean;
	total_capacity?: number;
	booked_participants?: number;
}

export interface TimeSlotWithAvailability extends TimeSlotInfo {
	service_id: string;
	employee_id?: string;
	equipment_requirements?: {
		equipment_id: string;
		required_capacity: number;
	}[];
}

export interface ReservationWithDetails extends Reservation {
	service: Service;
	customer: Profile;
	participants?: ReservationParticipant[];
}

// Booking flow types
export interface BookingFormData {
	serviceId: string;
	timeSlotId: string;
	tierParticipants: TierParticipantCount[]; // Simplified: just tier ID and count
	customerInfo: CustomerInfo;
	specialRequests?: string;
	selectedOptions?: string[]; // Array of selected option IDs
	discountCode?: string; // Discount coupon code
}

// Simplified participant count per pricing tier
export interface TierParticipantCount {
	tierId: string;
	count: number;
}

// Legacy participant info (deprecated - use TierParticipantCount instead)
export interface ParticipantInfo {
	age: number;
	firstName?: string;
	dietaryRestrictions?: string;
	medicalConditions?: string;
}

export type PaymentType = "deposit" | "full" | "installment";

export interface CustomerInfo {
	firstName: string;
	lastName: string;
	email: string;
	phone: string;
	emergencyContactName?: string;
	emergencyContactPhone?: string;
}

export interface ReservationParticipant {
	id: string;
	reservation_id: string;
	first_name: string;
	last_name: string;
	age: number;
	pricing_tier_id: string;
	individual_price: number;
	dietary_restrictions?: string;
	medical_conditions?: string;
}

// Pricing calculation types
export interface PriceCalculation {
	participants: {
		participant: ParticipantInfo;
		tier: PricingTier;
		price: number;
	}[];
	subtotal: number;
	discountAmount: number;
	total: number;
}

// Availability checking types
export interface AvailabilityRequest {
	serviceId: string;
	date: string;
	participantCount: number;
}

export interface AvailabilityResponse {
	date: string;
	timeSlots: TimeSlotInfo[];
}

// API response types
export interface ApiResponse<T> {
	data: T | null;
	error: string | null;
	success: boolean;
}

export interface PaginatedResponse<T> {
	data: T[];
	count: number;
	page: number;
	pageSize: number;
	totalPages: number;
}

// Error types
export interface AppError {
	code: string;
	message: string;
	details?: any;
}

// Booking status types
export type BookingStatus = "pending" | "confirmed" | "cancelled" | "completed" | "no_show";
export type TimeSlotStatus = "available" | "booked" | "cancelled" | "completed";
export type PaymentStatus = "pending" | "processing" | "completed" | "failed" | "refunded";

// Admin types
export interface DashboardStats {
	todayBookings: number;
	todayRevenue: number;
	weeklyBookings: number;
	weeklyRevenue: number;
	totalCustomers: number;
	activeServices: number;
}

export interface BookingListItem {
	id: string;
	reservation_number: string;
	customer_name: string;
	customer_email: string;
	service_name: string;
	date: string;
	time: string;
	participants: number;
	total_amount: number;
	status: BookingStatus;
	created_at: string;
}
