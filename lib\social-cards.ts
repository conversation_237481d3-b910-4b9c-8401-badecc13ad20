/**
 * Utility functions for generating social media cards
 */

export interface SocialCardConfig {
  title: string;
  subtitle?: string;
  description?: string;
  type?: "homepage" | "service" | "contact" | "about";
  image?: string;
}

/**
 * Generate Open Graph metadata for a page
 */
export function generateOpenGraphMetadata(config: SocialCardConfig) {
  const baseUrl = "https://www.soleiletdecouverte.com";
  
  // Generate dynamic social card URL
  const socialCardUrl = generateSocialCardUrl(config);
  
  return {
    title: config.title,
    description: config.description || "Découvrez la Guadeloupe authentique avec nos excursions éco-responsables.",
    images: [
      {
        url: socialCardUrl,
        width: 1200,
        height: 630,
        alt: config.title,
        type: "image/svg+xml",
      },
      // Fallback to static image
      {
        url: "/images/og-social-card.svg",
        width: 1200,
        height: 630,
        alt: config.title,
        type: "image/svg+xml",
      },
    ],
    siteName: "Soleil et Découverte",
    locale: "fr_FR",
    type: "website",
  };
}

/**
 * Generate Twitter Card metadata
 */
export function generateTwitterMetadata(config: SocialCardConfig) {
  const socialCardUrl = generateSocialCardUrl(config);
  
  return {
    card: "summary_large_image" as const,
    title: config.title,
    description: config.description || "Découvrez la Guadeloupe authentique avec nos excursions éco-responsables.",
    images: [socialCardUrl],
  };
}

/**
 * Generate URL for dynamic social card
 */
export function generateSocialCardUrl(config: SocialCardConfig): string {
  const params = new URLSearchParams();
  
  params.set("title", config.title);
  
  if (config.subtitle) {
    params.set("subtitle", config.subtitle);
  }
  
  if (config.type) {
    params.set("type", config.type);
  }
  
  return `/api/social-card?${params.toString()}`;
}

/**
 * Predefined configurations for common pages
 */
export const socialCardConfigs = {
  homepage: {
    title: "Soleil et Découverte",
    subtitle: "Excursions éco-responsables en Guadeloupe",
    description: "Découvrez la Guadeloupe authentique avec nos excursions éco-responsables. WaterBikes, visites culturelles et rencontres avec les pélicans.",
    type: "homepage" as const,
  },
  
  services: {
    title: "Nos Services",
    subtitle: "Excursions en Guadeloupe",
    description: "Découvrez toutes nos excursions éco-responsables : WaterBikes, visites culturelles, rencontres avec les pélicans et bien plus.",
    type: "service" as const,
  },
  
  contact: {
    title: "Contact",
    subtitle: "Soleil et Découverte",
    description: "Contactez-nous pour réserver votre excursion éco-responsable en Guadeloupe. Nous sommes à votre disposition.",
    type: "contact" as const,
  },
  
  reservation: {
    title: "Réservation",
    subtitle: "Excursions en Guadeloupe",
    description: "Réservez votre excursion éco-responsable en Guadeloupe. Réservation en ligne simple et sécurisée.",
    type: "homepage" as const,
  },
};

/**
 * Generate service-specific social card config
 */
export function generateServiceSocialCard(serviceName: string, serviceDescription?: string): SocialCardConfig {
  return {
    title: serviceName,
    subtitle: "Excursion éco-responsable",
    description: serviceDescription || `Découvrez ${serviceName} avec Soleil et Découverte en Guadeloupe.`,
    type: "service",
  };
}

/**
 * Get social card config for a specific page
 */
export function getSocialCardConfig(page: keyof typeof socialCardConfigs): SocialCardConfig {
  return socialCardConfigs[page];
}
